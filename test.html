<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="pageTitle">JEE Math Solutions</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            padding: 20px;
            background-color: #f4f4f9;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: auto;
            background: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        h1 {
            color: #0056b3;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #007bff;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
            margin-top: 40px;
            font-size: 1.8em;
        }
        h3 {
            color: #555;
            font-size: 1.4em;
            margin-top: 20px;
        }
        p {
            margin-bottom: 10px;
        }
        strong {
            color: #d9534f; /* A strong, noticeable color for bolded text */
        }
        .answer {
            background-color: #e6ffe6;
            padding: 10px;
            border-left: 5px solid #4CAF50;
            margin-top: 20px;
            font-weight: bold; /* Make the entire answer div text bold */
        }
        .note {
            background-color: #fff3cd;
            padding: 15px;
            border-left: 5px solid #ffc107;
            margin-top: 30px;
            color: #856404;
        }
        hr {
            border: 0;
            height: 1px;
            background-image: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.75), rgba(0, 0, 0, 0));
            margin: 40px 0;
        }
    </style>
    <!-- Include React and ReactDOM -->
    <script src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <!-- Include better-react-mathjax -->
    <script src="https://unpkg.com/better-react-mathjax@2.0.2/dist/index.umd.js"></script>
</head>
<body>
    <div id="root"></div>

    <script>
        // Sample response data (same as in the original)
        const responseData = {
            response: {
                response: `Let’s clarify your question: '(Image): Here’s the extracted text and questions from the image:\n\n---\n\n**Q.1** If \\( S \\) and \\( P \\) are sum and product respectively of all real values of \\( x \\) satisfying \\( |4 - |x - 1|| = 3 \\), then -  \n(A) \\( S + P = 4 \\)  \n(B) \\( S - P = 4 \\)  \n(C) \\( S^2 - P^2 = 16 \\)  \n(D) \\( S^2 + P^2 = 16 \\)  \n\n**Q.2** Value of  \n\\[\n\\left( \\frac{81}{16} \\right)^{-\\frac{3}{4}} \\times \\left( \\frac{25}{9} \\right)^{-\\frac{3}{2}} \\times \\left( \\frac{5}{2} \\right)^{-3}\n\\]\nis less than  \n(A) \\( \\frac{2}{3} \\)  \n(B) \\( \\frac{3}{2} \\)  \n(C) \\( 2 \\)  \n\n**Q.3** If \\( (7 - 4\\sqrt{3})^{2 - 4x + 3} + (7 + 4\\sqrt{3})^{2 - 4x + 3} = 14 \\), then the value of \\( x \\) is given by  \n(A) \\( 2 \\)  \n(B) \\( 2 - \\sqrt{2} \\)  \n(C) \\( 2 + \\sqrt{2} \\)  \n(D) \\( 2 - 2 \\)  \n\n**Q.4** If \\( 0 < a < b < c \\), and the roots \\( \\alpha, \\beta \\) of the equation \\( ax^2 + bx + c = 0 \\) are imaginary, then:  \n(A) \\( |\\alpha| = |\\beta| \\)  \n(B) \\( |\\alpha| > 1 \\)  \n(C) \\( |\\beta| < 1 \\)  \n(D) None of these  \n\n**Q.5** The value of \\( x \\) satisfying the equation \\( 2^{2x} - 8x - 2^{2 - 12} = 0 \\) is  \n(A) \\( 1 + \\log_2 3 \\)  \n(B) \\( \\frac{1}{2} \\log_6 2 \\)  \n(C) \\( 1 + \\log_2 \\frac{3}{2} \\)  \n(D) \\( 1 \\)  \n\n**Q.6** Number of real roots of the equation \\( (10 px^2 - qx + r) (px^2 - qx - 5r) (5px^2 - qx - r) = 0 \\), \\( (qpr \\neq 0) \\) can be -  \n(A) 0  \n(B) 2  \n(C) 4  \n(D) 6  \n\n---'. For example: Let’s solve this physics problem. To find force, use Newton’s Second Law, $F = ma$. For a 2 kg mass with 5 m/s² acceleration, $F = 2 \\times 5 = 10$ N. **Answer: 10 N**`,
                error: null
            },
            mode: "text",
            language: "tanglish",
            timestamp: "2025-07-31T11:46:17.693122",
            history: []
        };

        // React component to render the content
        const { MathJaxContext, MathJax } = window.MathJaxReact;

        function App() {
            const parseMarkdownToHtml = (markdown) => {
                let html = [];
                const lines = markdown.split('\n').map(line => line.trimEnd());
                let inList = false;
                let listItems = [];

                for (let i = 0; i < lines.length; i++) {
                    let line = lines[i];

                    if (line.startsWith('## ')) {
                        if (inList) {
                            html.push(<ul key={`ul-${i}`}>{listItems}</ul>);
                            listItems = [];
                            inList = false;
                        }
                        html.push(<h2 key={`h2-${i}`}>{line.substring(3)}</h2>);
                    } else if (line.startsWith('### ') || line.startsWith('**Step')) {
                        if (inList) {
                            html.push(<ul key={`ul-${i}`}>{listItems}</ul>);
                            listItems = [];
                            inList = false;
                        }
                        const headingText = line.startsWith('### ') ? line.substring(4) : line.replace(/\*\*(.*?)\*\*/g, '$1');
                        html.push(<h3 key={`h3-${i}`}>{headingText}</h3>);
                    } else if (line.startsWith('- ')) {
                        if (!inList) {
                            inList = true;
                        }
                        let listItemContent = line.substring(2);
                        listItemContent = listItemContent.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                        listItems.push(<li key={`li-${i}`}>
                            <MathJax>{convertLatexDelimiters(listItemContent)}</MathJax>
                        </li>);
                        if (i + 1 >= lines.length || !lines[i + 1].trimStart().startsWith('- ')) {
                            html.push(<ul key={`ul-${i}`}>{listItems}</ul>);
                            listItems = [];
                            inList = false;
                        }
                    } else if (line === '---') {
                        if (inList) {
                            html.push(<ul key={`ul-${i}`}>{listItems}</ul>);
                            listItems = [];
                            inList = false;
                        }
                        html.push(<hr key={`hr-${i}`} />);
                    } else if (line.startsWith('Answer:')) {
                        if (inList) {
                            html.push(<ul key={`ul-${i}`}>{listItems}</ul>);
                            listItems = [];
                            inList = false;
                        }
                        let answerContent = line.replace('Answer:', '').trim();
                        answerContent = answerContent.replace(/\*\*(.*?)\*\*/g, '$1');
                        html.push(
                            <div className="answer" key={`answer-${i}`}>
                                Answer: <strong><MathJax>{convertLatexDelimiters(answerContent)}</MathJax></strong>
                            </div>
                        );
                    } else if (line) {
                        if (inList) {
                            html.push(<ul key={`ul-${i}`}>{listItems}</ul>);
                            listItems = [];
                            inList = false;
                        }
                        line = line.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                        html.push(<p key={`p-${i}`}><MathJax>{convertLatexDelimiters(line)}</MathJax></p>);
                    } else {
                        if (inList) {
                            html.push(<ul key={`ul-${i}`}>{listItems}</ul>);
                            listItems = [];
                            inList = false;
                        }
                        html.push(<p key={`p-${i}`}></p>);
                    }
                }
                if (inList) {
                    html.push(<ul key="ul-final">{listItems}</ul>);
                }
                return html;
            };

            const convertLatexDelimiters = (text) => {
                // Convert inline LaTeX: \(...\) to $...$
                text = text.replace(/\\\((.*?)\\\)/g, '$ $1 $');
                // Convert display LaTeX: \[...\] to $$...$$
                text = text.replace(/\\\[(.*?)\\]/g, '$$ $1 $$');
                return text;
            };

            const parsedContent = parseMarkdownToHtml(responseData.response.response);

            return (
                <div className="container" id="contentContainer">
                    <h1>JEE Math Solutions</h1>
                    {parsedContent}
                </div>
            );
        }

        // Render the React component
        document.addEventListener('DOMContentLoaded', () => {
            const root = ReactDOM.createRoot(document.getElementById('root'));
            root.render(
                <MathJaxContext>
                    <App />
                </MathJaxContext>
            );
        });
    </script>
</body>
</html>